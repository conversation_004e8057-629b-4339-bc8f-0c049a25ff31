export interface ValidationResult {
  isValid: boolean;
  message: string;
  type: 'success' | 'error' | 'warning';
}

export interface PasswordValidation {
  isValid: boolean;
  requirements: {
    startsWithUppercase: boolean;
    hasMinLength: boolean;
    hasLetters: boolean;
    hasNumbers: boolean;
    hasSpecialChar: boolean;
    hasMinLetters: boolean;
  };
  strength: 'weak' | 'medium' | 'strong';
  message: string;
}

// Email validation with real-time checking
export const validateEmail = (email: string): ValidationResult => {
  if (!email) {
    return {
      isValid: false,
      message: 'Email is required',
      type: 'error'
    };
  }

  // Basic email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      message: 'Please enter a valid email address',
      type: 'error'
    };
  }

  // Check for common email providers (basic existence check)
  const commonProviders = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 
    'icloud.com', 'aol.com', 'live.com', 'msn.com'
  ];
  
  const domain = email.split('@')[1]?.toLowerCase();
  const isCommonProvider = commonProviders.includes(domain);
  
  if (!isCommonProvider && domain) {
    // For non-common providers, we'll assume they're valid but show a warning
    return {
      isValid: true,
      message: 'Please ensure this email address is correct',
      type: 'warning'
    };
  }

  return {
    isValid: true,
    message: 'Valid email address',
    type: 'success'
  };
};

// Advanced email existence check (simulated - in real app you'd use an API)
export const checkEmailExists = async (email: string): Promise<ValidationResult> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Simulate some emails as "already exists"
  const existingEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  if (existingEmails.includes(email.toLowerCase())) {
    return {
      isValid: false,
      message: 'This email is already registered',
      type: 'error'
    };
  }
  
  return {
    isValid: true,
    message: 'Email is available',
    type: 'success'
  };
};

// Password validation with your specific requirements
export const validatePassword = (password: string): PasswordValidation => {
  const requirements = {
    startsWithUppercase: /^[A-Z]/.test(password),
    hasMinLength: password.length >= 8,
    hasLetters: /[a-zA-Z]/.test(password),
    hasNumbers: /\d/.test(password),
    hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
    hasMinLetters: (password.match(/[a-zA-Z]/g) || []).length >= 3
  };

  const validCount = Object.values(requirements).filter(Boolean).length;
  const isValid = Object.values(requirements).every(Boolean);

  let strength: 'weak' | 'medium' | 'strong' = 'weak';
  // Only consider it strong if ALL requirements are met
  if (isValid) {
    strength = 'strong';
  } else if (validCount >= 4) {
    strength = 'medium';
  }

  let message = '';
  if (isValid) {
    message = 'Strong password!';
  } else {
    const missing = [];
    if (!requirements.startsWithUppercase) missing.push('start with uppercase letter');
    if (!requirements.hasMinLength) missing.push('be at least 8 characters');
    if (!requirements.hasMinLetters) missing.push('contain at least 3 letters');
    if (!requirements.hasNumbers) missing.push('contain numbers');
    if (!requirements.hasSpecialChar) missing.push('contain special characters');

    message = `Password must ${missing.join(', ')}`;
  }

  return {
    isValid,
    requirements,
    strength,
    message
  };
};

// Name validation
export const validateName = (name: string): ValidationResult => {
  if (!name.trim()) {
    return {
      isValid: false,
      message: 'Name is required',
      type: 'error'
    };
  }

  if (name.trim().length < 2) {
    return {
      isValid: false,
      message: 'Name must be at least 2 characters',
      type: 'error'
    };
  }

  if (!/^[a-zA-Z\s]+$/.test(name)) {
    return {
      isValid: false,
      message: 'Name can only contain letters and spaces',
      type: 'error'
    };
  }

  return {
    isValid: true,
    message: 'Valid name',
    type: 'success'
  };
};

// Password confirmation validation
export const validatePasswordConfirmation = (password: string, confirmPassword: string): ValidationResult => {
  if (!confirmPassword) {
    return {
      isValid: false,
      message: 'Please confirm your password',
      type: 'error'
    };
  }

  if (password !== confirmPassword) {
    return {
      isValid: false,
      message: 'Passwords do not match',
      type: 'error'
    };
  }

  return {
    isValid: true,
    message: 'Passwords match',
    type: 'success'
  };
};
