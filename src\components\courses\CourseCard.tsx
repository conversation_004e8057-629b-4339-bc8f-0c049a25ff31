
import React from 'react';
import { Link } from 'react-router-dom';
import { Play, Clock, Lock, Download } from 'lucide-react';
import { Course } from '../../types';
import { useAuth } from '../../contexts/AuthContext';

interface CourseCardProps {
  course: Course;
}

const CourseCard: React.FC<CourseCardProps> = ({ course }) => {
  const { user } = useAuth();
  
  const canAccessFull = user && (user.subscription_status === 'premium' || !course.is_premium);
  const showPreview = course.is_premium && (!user || user.subscription_status === 'free');

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return hours > 0 ? `${hours}h ${remainingMinutes}m` : `${remainingMinutes}m`;
  };

  const getDestinationLink = () => {
    if (canAccessFull) {
      return `/course/${course.id}`;
    } else if (user) {
      return '/pricing';
    } else {
      return '/login';
    }
  };

  return (
    <Link to={getDestinationLink()} className="block">
      <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer">
        <div className="relative">
          <img
            src={course.thumbnail_url}
            alt={course.title}
            className="w-full h-48 object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
            <Play className="h-12 w-12 text-white opacity-0 hover:opacity-100 transition-opacity duration-300" />
          </div>
          {showPreview && (
            <div className="absolute top-2 right-2 bg-primary-600 text-white px-2 py-1 rounded text-xs font-medium flex items-center">
              <Lock className="h-3 w-3 mr-1" />
              Premium
            </div>
          )}
        </div>
        
        <div className="p-6">
          <div className="flex items-center justify-end mb-2">
            <div className="flex items-center text-gray-500 text-sm">
              <Clock className="h-4 w-4 mr-1" />
              {formatDuration(course.duration)}
            </div>
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
            {course.title}
          </h3>
          
          <p className="text-gray-600 text-sm mb-4 line-clamp-3">
            {course.description}
          </p>
          
          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              {showPreview && (
                <Link
                  to={`/course/${course.id}/preview`}
                  className="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-gray-200 text-gray-700 hover:bg-gray-300"
                  onClick={(e) => e.stopPropagation()}
                >
                  Preview
                  <Play className="h-4 w-4 ml-1" />
                </Link>
              )}
            </div>
            
            {canAccessFull && (
              <button 
                className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors bg-secondary-100 text-secondary-700 hover:bg-secondary-200"
                onClick={(e) => e.stopPropagation()}
              >
                <Download className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CourseCard;
