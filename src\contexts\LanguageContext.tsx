
import React, { createContext, useContext, useState, useEffect } from 'react';

export type Language = 'fr' | 'ar';

interface Translations {
  [key: string]: string;
}

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const translations: Record<Language, Translations> = {
  fr: {
    // Navigation
    'nav.courses': 'Cours',
    'nav.about': 'À propos',
    'nav.contact': 'Contact',
    'nav.signin': 'Se connecter',
    'nav.getstarted': 'Commencer',
    'nav.upgrade': 'Améliorer',
    'nav.dashboard': 'Tableau de bord',
    
    // Home page
    'home.hero.title': 'Avancez votre',
    'home.hero.title.highlight': ' Carrière en Pharmacie',
    'home.hero.description': 'Accédez à des programmes éducatifs complets en pharmacie conçus par des experts de l\'industrie. Apprenez à votre rythme avec du contenu vidéo de haute qualité et des perspectives pratiques.',
    'home.hero.explore': 'Explorer les cours',
    'home.hero.trial': 'Essai gratuit',
    
    'home.features.title': 'Pourquoi choisir PharmEd Pro?',
    'home.features.subtitle': 'Formation pharmaceutique professionnelle qui s\'adapte à votre emploi du temps',
    'home.features.expert.title': 'Contenu Expert',
    'home.features.expert.desc': 'Apprenez auprès de professionnels de l\'industrie avec des années d\'expérience en pharmacie',
    'home.features.community.title': 'Communauté',
    'home.features.community.desc': 'Rejoignez des milliers de professionnels en pharmacie qui font progresser leur carrière',
    'home.features.certificates.title': 'Certifications',
    'home.features.certificates.desc': 'Obtenez des certificats professionnels pour valider votre expertise',
    'home.features.secure.title': 'Plateforme Sécurisée',
    'home.features.secure.desc': 'Vos données et progrès sont protégés avec une sécurité de niveau entreprise',
    
    'home.cta.title': 'Prêt à commencer l\'apprentissage?',
    'home.cta.description': 'Rejoignez des milliers de professionnels en pharmacie qui font confiance à PharmEd Pro pour leur formation continue',
    'home.cta.button': 'Commencer aujourd\'hui',
    
    // Common
    'common.loading': 'Chargement...',
    'common.error': 'Erreur',
    'common.success': 'Succès',
    'common.search': 'Rechercher...',
    'common.filter': 'Filtrer',
    'common.all': 'Tout',
    
    // Courses
    'courses.title': 'Cours de Pharmacie',
    'courses.subtitle': 'Avancez vos connaissances pharmaceutiques avec notre bibliothèque de cours complète',
    'courses.search.placeholder': 'Rechercher des cours...',
    'courses.category.all': 'Toutes les catégories',
    'courses.level.all': 'Tous les niveaux',
    'courses.level.beginner': 'Débutant',
    'courses.level.intermediate': 'Intermédiaire',
    'courses.level.advanced': 'Avancé',
    'courses.showing': 'Affichage de',
    'courses.of': 'sur',
    'courses.noresults.title': 'Aucun cours trouvé',
    'courses.noresults.desc': 'Essayez d\'ajuster vos termes de recherche ou filtres',
    
    // Pricing
    'pricing.title': 'Choisissez votre plan d\'apprentissage',
    'pricing.subtitle': 'Investissez dans votre carrière pharmaceutique avec nos programmes éducatifs complets',
    'pricing.plan.basic': 'Plan de Base',
    'pricing.plan.premium': 'Plan Premium',
    'pricing.current': 'Plan actuel',
    'pricing.upgrade': 'Améliorer maintenant',
    'pricing.faq.title': 'Questions fréquemment posées',

    // About
    'about.title': 'À propos de PharmEd Pro',
    'about.subtitle': 'Notre mission est de révolutionner l\'éducation pharmaceutique',
    'about.mission.title': 'Notre Mission',
    'about.mission.desc': 'Nous nous engageons à fournir une éducation pharmaceutique de classe mondiale accessible à tous les professionnels de la santé.',
    'about.vision.title': 'Notre Vision',
    'about.vision.desc': 'Devenir la plateforme de référence pour l\'apprentissage continu en pharmacie dans le monde francophone.',
    'about.values.title': 'Nos Valeurs',
    'about.values.excellence': 'Excellence académique',
    'about.values.accessibility': 'Accessibilité pour tous',
    'about.values.innovation': 'Innovation pédagogique',

    // Contact
    'contact.title': 'Contactez-nous',
    'contact.subtitle': 'Nous sommes là pour vous aider',
    'contact.form.name': 'Nom complet',
    'contact.form.email': 'Adresse email',
    'contact.form.subject': 'Sujet',
    'contact.form.message': 'Message',
    'contact.form.send': 'Envoyer le message',
    'contact.info.address': 'Adresse',
    'contact.info.phone': 'Téléphone',
    'contact.info.email': 'Email',
    'contact.success': 'Message envoyé avec succès!'
  },
  ar: {
    // Navigation
    'nav.courses': 'الدورات',
    'nav.about': 'حولنا',
    'nav.contact': 'اتصل بنا',
    'nav.signin': 'تسجيل الدخول',
    'nav.getstarted': 'ابدأ الآن',
    'nav.upgrade': 'ترقية',
    'nav.dashboard': 'لوحة التحكم',
    
    // Home page
    'home.hero.title': 'طور حياتك المهنية في',
    'home.hero.title.highlight': ' علوم الصيدلة',
    'home.hero.description': 'احصل على برامج تعليمية شاملة في الصيدلة مصممة من قبل خبراء الصناعة. تعلم وفقاً لوتيرتك الخاصة مع محتوى فيديو عالي الجودة ورؤى عملية.',
    'home.hero.explore': 'استكشف الدورات',
    'home.hero.trial': 'تجربة مجانية',
    
    'home.features.title': 'لماذا تختار PharmEd Pro؟',
    'home.features.subtitle': 'تعليم صيدلاني مهني يتناسب مع جدولك الزمني',
    'home.features.expert.title': 'محتوى من الخبراء',
    'home.features.expert.desc': 'تعلم من المتخصصين في الصناعة مع سنوات من الخبرة في مجال الصيدلة',
    'home.features.community.title': 'مجتمع متخصص',
    'home.features.community.desc': 'انضم إلى آلاف المتخصصين في الصيدلة الذين يطورون مسيرتهم المهنية',
    'home.features.certificates.title': 'شهادات معتمدة',
    'home.features.certificates.desc': 'احصل على شهادات مهنية للتحقق من خبرتك ومهاراتك',
    'home.features.secure.title': 'منصة آمنة',
    'home.features.secure.desc': 'بياناتك وتقدمك محميان بأمان على مستوى الشركات',
    
    'home.cta.title': 'هل أنت مستعد لبدء التعلم؟',
    'home.cta.description': 'انضم إلى آلاف المتخصصين في الصيدلة الذين يثقون في PharmEd Pro لتعليمهم المستمر',
    'home.cta.button': 'ابدأ اليوم',
    
    // Common
    'common.loading': 'جاري التحميل...',
    'common.error': 'خطأ',
    'common.success': 'نجح',
    'common.search': 'بحث...',
    'common.filter': 'تصفية',
    'common.all': 'الكل',
    
    // Courses
    'courses.title': 'دورات الصيدلة',
    'courses.subtitle': 'طور معرفتك الصيدلانية مع مكتبة الدورات الشاملة لدينا',
    'courses.search.placeholder': 'البحث عن الدورات...',
    'courses.category.all': 'جميع الفئات',
    'courses.level.all': 'جميع المستويات',
    'courses.level.beginner': 'مبتدئ',
    'courses.level.intermediate': 'متوسط',
    'courses.level.advanced': 'متقدم',
    'courses.showing': 'عرض',
    'courses.of': 'من أصل',
    'courses.noresults.title': 'لم يتم العثور على دورات',
    'courses.noresults.desc': 'حاول تعديل مصطلحات البحث أو المرشحات',
    
    // Pricing
    'pricing.title': 'اختر خطة التعلم المناسبة لك',
    'pricing.subtitle': 'استثمر في مسيرتك المهنية الصيدلانية مع برامجنا التعليمية الشاملة',
    'pricing.plan.basic': 'الخطة الأساسية',
    'pricing.plan.premium': 'الخطة المميزة',
    'pricing.current': 'الخطة الحالية',
    'pricing.upgrade': 'ترقية الآن',
    'pricing.faq.title': 'الأسئلة الشائعة',

    // About
    'about.title': 'حول PharmEd Pro',
    'about.subtitle': 'مهمتنا هي ثورة التعليم الصيدلاني',
    'about.mission.title': 'مهمتنا',
    'about.mission.desc': 'نحن ملتزمون بتوفير تعليم صيدلاني عالمي المستوى يمكن الوصول إليه لجميع المتخصصين في الرعاية الصحية.',
    'about.vision.title': 'رؤيتنا',
    'about.vision.desc': 'أن نصبح المنصة المرجعية للتعلم المستمر في الصيدلة في العالم العربي.',
    'about.values.title': 'قيمنا',
    'about.values.excellence': 'التميز الأكاديمي',
    'about.values.accessibility': 'إمكانية الوصول للجميع',
    'about.values.innovation': 'الابتكار التعليمي',

    // Contact
    'contact.title': 'تواصل معنا',
    'contact.subtitle': 'نحن هنا لمساعدتك',
    'contact.form.name': 'الاسم الكامل',
    'contact.form.email': 'البريد الإلكتروني',
    'contact.form.subject': 'الموضوع',
    'contact.form.message': 'الرسالة',
    'contact.form.send': 'إرسال الرسالة',
    'contact.info.address': 'العنوان',
    'contact.info.phone': 'الهاتف',
    'contact.info.email': 'البريد الإلكتروني',
    'contact.success': 'تم إرسال الرسالة بنجاح!'
  }
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('fr'); // French as default

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'fr' || savedLanguage === 'ar')) {
      setLanguage(savedLanguage);
    }
  }, []);

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('language', lang);
  };

  const t = (key: string): string => {
    return translations[language]?.[key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
