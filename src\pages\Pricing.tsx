import React from 'react';
import { Check, Play } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

const Pricing: React.FC = () => {
  const { language } = useLanguage();

  const plan = {
    name: language === 'ar' ? 'الخطة الأساسية' : 'Plan de Base',
    price: '2000',
    period: language === 'ar' ? 'دج' : 'DA',
    description: language === 'ar' ? 'احصل على الوصول الكامل لجميع مقاطع الفيديو التعليمية' : 'Obtenez un accès complet à toutes les vidéos éducatives',
    features: language === 'ar' ? [
      'وصول غير محدود لجميع مقاطع الفيديو',
      'محتوى تعليمي عالي الجودة',
      'دروس مفصلة في الصيدلة',
      'موارد قابلة للتحميل',
      'شهادة إتمام',
      'دعم عبر البريد الإلكتروني',
      'مشاهدة غير متصلة بالإنترنت'
    ] : [
      'Accès illimité à toutes les vidéos',
      'Contenu éducatif de haute qualité',
      'Cours détaillés en pharmacie',
      'Ressources téléchargeables',
      'Certificat d\'achèvement',
      'Support par email',
      'Visionnage hors ligne'
    ],
    buttonText: language === 'ar' ? 'ابدأ التعلم الآن' : 'Commencer l\'apprentissage'
  };

  const handleUpgrade = () => {
    console.log('Starting subscription to basic plan');
    alert('Chargily payment integration would be triggered here for the basic plan');
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="animate-fade-in">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {language === 'ar' ? 'احصل على الوصول الكامل' : 'Obtenez un accès complet'}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {language === 'ar' 
              ? 'انضم إلى آلاف الطلاب والمهنيين واحصل على الوصول الكامل لجميع مقاطع الفيديو التعليمية في الصيدلة'
              : 'Rejoignez des milliers d\'étudiants et de professionnels et obtenez un accès complet à toutes les vidéos éducatives en pharmacie'
            }
          </p>
        </div>

        {/* Single Pricing Card */}
        <div className="max-w-md mx-auto mb-16">
          <div className="relative bg-white rounded-2xl shadow-xl overflow-hidden ring-2 ring-primary-500 transform scale-105">
            <div className="absolute top-0 left-0 right-0 bg-primary-600 text-white text-center py-3 text-sm font-medium">
              <Play className="inline h-4 w-4 mr-1" />
              {language === 'ar' ? 'الوصول الكامل لجميع الفيديوهات' : 'Accès complet à toutes les vidéos'}
            </div>

            <div className="p-8 pt-20">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <div className="mb-4">
                  <span className="text-5xl font-bold text-gray-900">{plan.price}</span>
                  <span className="text-2xl text-gray-600 ml-2">{plan.period}</span>
                </div>
                <p className="text-gray-600 text-lg">{plan.description}</p>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                onClick={handleUpgrade}
                className="w-full py-4 px-6 bg-primary-600 text-white rounded-lg font-medium text-lg hover:bg-primary-700 transition-colors"
              >
                {plan.buttonText}
              </button>
            </div>
          </div>
        </div>

        {/* Value Proposition */}
        <div className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-2xl p-8 mb-16">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {language === 'ar' ? 'لماذا تختار PharmEd Pro؟' : 'Pourquoi choisir PharmEd Pro?'}
            </h2>
            <div className="grid md:grid-cols-3 gap-8 mt-8">
              <div className="text-center">
                <div className="bg-primary-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Play className="h-6 w-6 text-primary-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {language === 'ar' ? 'محتوى شامل' : 'Contenu complet'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {language === 'ar' 
                    ? 'جميع مقاطع الفيديو التي تحتاجها في مكان واحد'
                    : 'Toutes les vidéos dont vous avez besoin en un seul endroit'
                  }
                </p>
              </div>
              <div className="text-center">
                <div className="bg-secondary-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Check className="h-6 w-6 text-secondary-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {language === 'ar' ? 'جودة عالية' : 'Haute qualité'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {language === 'ar' 
                    ? 'محتوى تعليمي عالي الجودة من خبراء الصيدلة'
                    : 'Contenu éducatif de haute qualité par des experts en pharmacie'
                  }
                </p>
              </div>
              <div className="text-center">
                <div className="bg-primary-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Play className="h-6 w-6 text-primary-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {language === 'ar' ? 'تعلم بمرونة' : 'Apprentissage flexible'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {language === 'ar' 
                    ? 'شاهد في أي وقت، في أي مكان، بالسرعة التي تناسبك'
                    : 'Regardez quand vous voulez, où vous voulez, à votre rythme'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <p className="text-gray-600 mb-4">
            {language === 'ar' ? 'لديك أسئلة؟ ' : 'Vous avez des questions? '}
            <Link to="/contact" className="text-primary-600 hover:text-primary-700 font-medium">
              {language === 'ar' ? 'اتصل بفريق الدعم' : 'Contactez notre équipe de support'}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
