// Simple in-memory rate limiter
const attempts = new Map();

const rateLimiter = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
  return (req, res, next) => {
    const key = req.ip + ':' + req.route.path;
    const now = Date.now();
    
    // Clean up old entries
    for (const [k, v] of attempts.entries()) {
      if (now - v.firstAttempt > windowMs) {
        attempts.delete(k);
      }
    }
    
    // Get current attempts for this IP/route
    const current = attempts.get(key) || { count: 0, firstAttempt: now };
    
    // Reset if window has passed
    if (now - current.firstAttempt > windowMs) {
      current.count = 0;
      current.firstAttempt = now;
    }
    
    // Check if limit exceeded
    if (current.count >= maxAttempts) {
      return res.status(429).json({
        message: 'Too many attempts. Please try again later.',
        retryAfter: Math.ceil((windowMs - (now - current.firstAttempt)) / 1000)
      });
    }
    
    // Increment counter
    current.count++;
    attempts.set(key, current);
    
    next();
  };
};

module.exports = rateLimiter;
