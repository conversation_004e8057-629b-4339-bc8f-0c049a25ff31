import { ValidationResult } from '../utils/validation';

// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Email validation service
class EmailValidationService {
  private cache = new Map<string, ValidationResult>();
  private pendingRequests = new Map<string, Promise<ValidationResult>>();

  // Check if email format is valid
  validateFormat(email: string): ValidationResult {
    if (!email) {
      return {
        isValid: false,
        message: 'Email is required',
        type: 'error'
      };
    }

    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      return {
        isValid: false,
        message: 'Please enter a valid email address',
        type: 'error'
      };
    }

    return {
      isValid: true,
      message: 'Valid email format',
      type: 'success'
    };
  }

  // Check if email exists (simulated API call)
  async checkEmailExists(email: string): Promise<ValidationResult> {
    // Return cached result if available
    if (this.cache.has(email)) {
      return this.cache.get(email)!;
    }

    // Return pending request if already in progress
    if (this.pendingRequests.has(email)) {
      return this.pendingRequests.get(email)!;
    }

    // Create new request
    const request = this.performEmailCheck(email);
    this.pendingRequests.set(email, request);

    try {
      const result = await request;
      this.cache.set(email, result);
      return result;
    } finally {
      this.pendingRequests.delete(email);
    }
  }

  private async performEmailCheck(email: string): Promise<ValidationResult> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Simulate some emails as already existing
    const existingEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    if (existingEmails.includes(email.toLowerCase())) {
      return {
        isValid: false,
        message: 'This email is already registered',
        type: 'error'
      };
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /^[a-z]+\d+@/,  // Simple patterns like user123@
      /test|demo|sample|fake/i,  // Test emails
    ];

    const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(email));
    if (isSuspicious) {
      return {
        isValid: true,
        message: 'Please ensure this is a valid email address',
        type: 'warning'
      };
    }

    // Check domain reputation (simplified)
    const domain = email.split('@')[1]?.toLowerCase();
    const trustedDomains = [
      'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
      'icloud.com', 'aol.com', 'live.com', 'msn.com',
      'protonmail.com', 'zoho.com'
    ];

    if (trustedDomains.includes(domain)) {
      return {
        isValid: true,
        message: 'Email is available',
        type: 'success'
      };
    }

    // For unknown domains, show warning but allow
    return {
      isValid: true,
      message: 'Email appears to be available (please verify domain)',
      type: 'warning'
    };
  }

  // Clear cache (useful for testing or when user logs out)
  clearCache(): void {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  // Get cached result without making new request
  getCachedResult(email: string): ValidationResult | null {
    return this.cache.get(email) || null;
  }
}

// Export singleton instance
export const emailValidationService = new EmailValidationService();

// Hook for React components
export const useEmailValidation = () => {
  return {
    validateFormat: emailValidationService.validateFormat.bind(emailValidationService),
    checkExists: emailValidationService.checkEmailExists.bind(emailValidationService),
    getCached: emailValidationService.getCachedResult.bind(emailValidationService),
    clearCache: emailValidationService.clearCache.bind(emailValidationService)
  };
};
