import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Mail, Lock, Eye, EyeOff, AlertCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { ValidatedInput, ValidationFeedback } from '../../components/auth/ValidationFeedback';
import { validateEmail, validatePassword } from '../../utils/validation';
import { useEmailValidation, debounce } from '../../services/emailValidation';

const Login: React.FC = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationState, setValidationState] = useState({
    email: { isValid: false, message: '', type: 'error' as const },
    password: { isValid: false, message: '', type: 'error' as const }
  });
  const [isEmailChecking, setIsEmailChecking] = useState(false);
  const [attemptCount, setAttemptCount] = useState(0);

  const { signIn } = useAuth();
  const navigate = useNavigate();
  const { validateFormat } = useEmailValidation();

  // Debounced email validation
  const debouncedEmailValidation = debounce((email: string) => {
    if (email) {
      const formatValidation = validateFormat(email);
      setValidationState(prev => ({
        ...prev,
        email: formatValidation
      }));
    }
  }, 300);

  // Real-time validation
  useEffect(() => {
    if (formData.email) {
      debouncedEmailValidation(formData.email);
    } else {
      setValidationState(prev => ({
        ...prev,
        email: { isValid: false, message: '', type: 'error' }
      }));
    }
  }, [formData.email]);

  useEffect(() => {
    if (formData.password) {
      const passwordValidation = validatePassword(formData.password);
      setValidationState(prev => ({
        ...prev,
        password: {
          isValid: passwordValidation.isValid,
          message: passwordValidation.isValid ? 'Password looks good' : 'Password does not meet requirements',
          type: passwordValidation.isValid ? 'success' : 'error'
        }
      }));
    } else {
      setValidationState(prev => ({
        ...prev,
        password: { isValid: false, message: '', type: 'error' }
      }));
    }
  }, [formData.password]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setAttemptCount(prev => prev + 1);

    // Validate form before submission
    if (!validationState.email.isValid || !validationState.password.isValid) {
      setError('Please fix the validation errors before submitting');
      return;
    }

    // Rate limiting - prevent too many attempts
    if (attemptCount >= 5) {
      setError('Too many login attempts. Please wait a moment before trying again.');
      return;
    }

    setLoading(true);

    try {
      await signIn(formData.email, formData.password);
      navigate('/dashboard');
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to sign in';
      setError(errorMessage);

      // Provide helpful hints based on error
      if (errorMessage.includes('Invalid credentials')) {
        setError('Invalid email or password. Please check your credentials and try again.');
      } else if (errorMessage.includes('Network')) {
        setError('Network error. Please check your connection and try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center animate-fade-in">
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Or{' '}
            <Link
              to="/register"
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              create a new account
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6 animate-slide-up" onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-50 border border-red-300 text-red-700 px-4 py-3 rounded-lg flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {attemptCount >= 3 && attemptCount < 5 && (
            <div className="bg-yellow-50 border border-yellow-300 text-yellow-700 px-4 py-3 rounded-lg flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 flex-shrink-0" />
              <span>Multiple failed attempts detected. Please ensure your credentials are correct.</span>
            </div>
          )}

          <div className="space-y-6">
            <ValidatedInput
              label="Email Address"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              validation={validationState.email}
              isLoading={isEmailChecking}
              placeholder="Enter your email address"
              autoComplete="email"
              required
              icon={<Mail className="h-5 w-5 text-gray-400" />}
            />

            <ValidatedInput
              label="Password"
              type={showPassword ? 'text' : 'password'}
              name="password"
              value={formData.password}
              onChange={handleChange}
              validation={validationState.password}
              placeholder="Enter your password"
              autoComplete="current-password"
              required
              icon={<Lock className="h-5 w-5 text-gray-400" />}
              showToggle={showPassword ? <EyeOff className="h-5 w-5 text-gray-400" /> : <Eye className="h-5 w-5 text-gray-400" />}
              onToggle={() => setShowPassword(!showPassword)}
            />
          </div>

          <div>
            <button
              type="submit"
              disabled={loading || !validationState.email.isValid || !validationState.password.isValid || attemptCount >= 5}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Signing in...</span>
                </div>
              ) : (
                'Sign in'
              )}
            </button>
          </div>

          <div className="text-center">
            <Link
              to="/forgot-password"
              className="text-sm text-primary-600 hover:text-primary-500"
            >
              Forgot your password?
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;