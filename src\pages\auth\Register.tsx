import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Mail, Lock, User, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { ValidatedInput, PasswordStrength, PasswordRequirements } from '../../components/auth/ValidationFeedback';
import { validateEmail, validatePassword, validateName, validatePasswordConfirmation } from '../../utils/validation';
import { useEmailValidation, debounce } from '../../services/emailValidation';

const Register: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationState, setValidationState] = useState({
    name: { isValid: false, message: '', type: 'error' as const },
    email: { isValid: false, message: '', type: 'error' as const },
    password: { isValid: false, message: '', type: 'error' as const },
    confirmPassword: { isValid: false, message: '', type: 'error' as const }
  });
  const [passwordValidation, setPasswordValidation] = useState({
    isValid: false,
    requirements: {
      startsWithUppercase: false,
      hasMinLength: false,
      hasLetters: false,
      hasNumbers: false,
      hasSpecialChar: false,
      hasMinLetters: false
    },
    strength: 'weak' as const,
    message: ''
  });
  const [isEmailChecking, setIsEmailChecking] = useState(false);
  const [showPasswordRequirements, setShowPasswordRequirements] = useState(false);

  const { signUp } = useAuth();
  const navigate = useNavigate();
  const { validateFormat, checkExists } = useEmailValidation();

  // Debounced email validation
  const debouncedEmailValidation = debounce(async (email: string) => {
    if (email) {
      const formatValidation = validateFormat(email);
      if (formatValidation.isValid) {
        setIsEmailChecking(true);
        try {
          const existsValidation = await checkExists(email);
          setValidationState(prev => ({
            ...prev,
            email: existsValidation
          }));
        } catch (error) {
          setValidationState(prev => ({
            ...prev,
            email: { isValid: false, message: 'Error checking email availability', type: 'error' }
          }));
        } finally {
          setIsEmailChecking(false);
        }
      } else {
        setValidationState(prev => ({
          ...prev,
          email: formatValidation
        }));
      }
    }
  }, 500);

  // Real-time validation effects
  useEffect(() => {
    if (formData.name) {
      const nameValidation = validateName(formData.name);
      setValidationState(prev => ({
        ...prev,
        name: nameValidation
      }));
    } else {
      setValidationState(prev => ({
        ...prev,
        name: { isValid: false, message: '', type: 'error' }
      }));
    }
  }, [formData.name]);

  useEffect(() => {
    if (formData.email) {
      debouncedEmailValidation(formData.email);
    } else {
      setValidationState(prev => ({
        ...prev,
        email: { isValid: false, message: '', type: 'error' }
      }));
      setIsEmailChecking(false);
    }
  }, [formData.email]);

  useEffect(() => {
    if (formData.password) {
      const passwordValidationResult = validatePassword(formData.password);
      setPasswordValidation(passwordValidationResult);
      setValidationState(prev => ({
        ...prev,
        password: {
          isValid: passwordValidationResult.isValid,
          message: passwordValidationResult.message,
          type: passwordValidationResult.isValid ? 'success' : 'error'
        }
      }));
    } else {
      setPasswordValidation({
        isValid: false,
        requirements: {
          startsWithUppercase: false,
          hasMinLength: false,
          hasLetters: false,
          hasNumbers: false,
          hasSpecialChar: false,
          hasMinLetters: false
        },
        strength: 'weak',
        message: ''
      });
      setValidationState(prev => ({
        ...prev,
        password: { isValid: false, message: '', type: 'error' }
      }));
    }
  }, [formData.password]);

  useEffect(() => {
    if (formData.confirmPassword) {
      const confirmValidation = validatePasswordConfirmation(formData.password, formData.confirmPassword);
      setValidationState(prev => ({
        ...prev,
        confirmPassword: confirmValidation
      }));
    } else {
      setValidationState(prev => ({
        ...prev,
        confirmPassword: { isValid: false, message: '', type: 'error' }
      }));
    }
  }, [formData.password, formData.confirmPassword]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (error) setError('');

    // Show password requirements when user focuses on password field
    if (name === 'password' && value) {
      setShowPasswordRequirements(true);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Comprehensive validation before submission
    const allFieldsValid = Object.values(validationState).every(field => field.isValid);

    if (!allFieldsValid) {
      setError('Please fix all validation errors before submitting');
      return;
    }

    if (!passwordValidation.isValid) {
      setError('Password does not meet security requirements');
      return;
    }

    setLoading(true);

    try {
      await signUp(formData.email, formData.password, formData.name);
      navigate('/dashboard');
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create account';
      setError(errorMessage);

      // Provide helpful hints based on error
      if (errorMessage.includes('already exists')) {
        setError('An account with this email already exists. Please try logging in instead.');
      } else if (errorMessage.includes('Network')) {
        setError('Network error. Please check your connection and try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = Object.values(validationState).every(field => field.isValid) && passwordValidation.isValid;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center animate-fade-in">
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Create your account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Or{' '}
            <Link
              to="/login"
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              sign in to your existing account
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6 animate-slide-up" onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-50 border border-red-300 text-red-700 px-4 py-3 rounded-lg flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          <div className="space-y-6">
            <ValidatedInput
              label="Full Name"
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              validation={validationState.name}
              placeholder="Enter your full name"
              autoComplete="name"
              required
              icon={<User className="h-5 w-5 text-gray-400" />}
            />

            <ValidatedInput
              label="Email Address"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              validation={validationState.email}
              isLoading={isEmailChecking}
              placeholder="Enter your email address"
              autoComplete="email"
              required
              icon={<Mail className="h-5 w-5 text-gray-400" />}
            />

            <div className="space-y-3">
              <ValidatedInput
                label="Password"
                type={showPassword ? 'text' : 'password'}
                name="password"
                value={formData.password}
                onChange={handleChange}
                validation={validationState.password}
                placeholder="Create a strong password"
                autoComplete="new-password"
                required
                icon={<Lock className="h-5 w-5 text-gray-400" />}
                showToggle={showPassword ? <EyeOff className="h-5 w-5 text-gray-400" /> : <Eye className="h-5 w-5 text-gray-400" />}
                onToggle={() => setShowPassword(!showPassword)}
              />

              {formData.password && (
                <PasswordStrength validation={passwordValidation} />
              )}

              {showPasswordRequirements && formData.password && (
                <PasswordRequirements validation={passwordValidation} />
              )}
            </div>

            <ValidatedInput
              label="Confirm Password"
              type={showConfirmPassword ? 'text' : 'password'}
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              validation={validationState.confirmPassword}
              placeholder="Confirm your password"
              autoComplete="new-password"
              required
              icon={<Lock className="h-5 w-5 text-gray-400" />}
              showToggle={showConfirmPassword ? <EyeOff className="h-5 w-5 text-gray-400" /> : <Eye className="h-5 w-5 text-gray-400" />}
              onToggle={() => setShowConfirmPassword(!showConfirmPassword)}
            />
          </div>

          {isFormValid && (
            <div className="bg-green-50 border border-green-300 text-green-700 px-4 py-3 rounded-lg flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 flex-shrink-0" />
              <span>All requirements met! Ready to create your account.</span>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading || !isFormValid}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Creating account...</span>
                </div>
              ) : (
                'Create account'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Register;