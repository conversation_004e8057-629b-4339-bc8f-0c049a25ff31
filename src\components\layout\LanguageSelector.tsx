
import React from 'react';
import { Globe } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

const LanguageSelector: React.FC = () => {
  const { language, setLanguage } = useLanguage();

  return (
    <div className="relative">
      <div className="flex items-center space-x-2">
        <Globe className="h-4 w-4 text-gray-600" />
        <select
          value={language}
          onChange={(e) => setLanguage(e.target.value as 'fr' | 'ar')}
          className="bg-transparent border-none text-sm text-gray-600 hover:text-primary-600 focus:outline-none cursor-pointer"
        >
          <option value="fr">Français</option>
          <option value="ar">العربية</option>
        </select>
      </div>
    </div>
  );
};

export default LanguageSelector;
