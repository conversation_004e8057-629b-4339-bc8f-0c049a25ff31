
import React from 'react';
import { Target, Eye, Heart, Users, Award, BookOpen } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const About: React.FC = () => {
  const { t, language } = useLanguage();

  const values = [
    {
      icon: Award,
      title: t('about.values.excellence'),
      description: language === 'ar' 
        ? 'نلتزم بتقديم أعلى معايير الجودة في التعليم الصيدلاني'
        : 'Nous nous engageons à maintenir les plus hauts standards de qualité dans l\'éducation pharmaceutique'
    },
    {
      icon: Users,
      title: t('about.values.accessibility'),
      description: language === 'ar'
        ? 'نجعل التعليم متاحاً لجميع المتخصصين بغض النظر عن موقعهم أو ظروفهم'
        : 'Nous rendons l\'éducation accessible à tous les professionnels, peu importe leur localisation'
    },
    {
      icon: BookOpen,
      title: t('about.values.innovation'),
      description: language === 'ar'
        ? 'نستخدم أحدث التقنيات والطرق التعليمية لتحسين تجربة التعلم'
        : 'Nous utilisons les dernières technologies et méthodes pédagogiques pour améliorer l\'expérience d\'apprentissage'
    }
  ];

  const stats = [
    { number: '10,000+', label: language === 'ar' ? 'طالب' : 'Étudiants' },
    { number: '50+', label: language === 'ar' ? 'دورة' : 'Cours' },
    { number: '98%', label: language === 'ar' ? 'رضا الطلاب' : 'Satisfaction' },
    { number: '24/7', label: language === 'ar' ? 'دعم' : 'Support' }
  ];

  return (
    <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="animate-fade-in">
        {/* Hero Section */}
        <div className="text-center mb-20">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('about.title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('about.subtitle')}
          </p>
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-12 mb-20">
          <div className="bg-white rounded-2xl shadow-lg p-8 animate-slide-up">
            <div className="flex items-center mb-6">
              <Target className={`h-8 w-8 text-primary-600 ${language === 'ar' ? 'ml-4' : 'mr-4'}`} />
              <h2 className="text-2xl font-bold text-gray-900">{t('about.mission.title')}</h2>
            </div>
            <p className="text-gray-700 leading-relaxed">
              {t('about.mission.desc')}
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-8 animate-slide-up" style={{ animationDelay: '0.1s' }}>
            <div className="flex items-center mb-6">
              <Eye className={`h-8 w-8 text-primary-600 ${language === 'ar' ? 'ml-4' : 'mr-4'}`} />
              <h2 className="text-2xl font-bold text-gray-900">{t('about.vision.title')}</h2>
            </div>
            <p className="text-gray-700 leading-relaxed">
              {t('about.vision.desc')}
            </p>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">{t('about.values.title')}</h2>
            <p className="text-xl text-gray-600">
              {language === 'ar' 
                ? 'القيم التي توجه عملنا وتشكل مستقبل التعليم الصيدلاني'
                : 'Les valeurs qui guident notre travail et façonnent l\'avenir de l\'éducation pharmaceutique'
              }
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div 
                key={index}
                className="bg-white rounded-2xl shadow-lg p-8 text-center animate-slide-up hover:shadow-xl transition-shadow"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                  <value.icon className="h-8 w-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                <p className="text-gray-700 leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Stats Section */}
        <div className="bg-primary-600 rounded-2xl p-12 text-white">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              {language === 'ar' ? 'إنجازاتنا بالأرقام' : 'Nos réalisations en chiffres'}
            </h2>
            <p className="text-primary-100 text-lg">
              {language === 'ar' 
                ? 'نفخر بالثقة التي يضعها المتخصصون في منصتنا'
                : 'Nous sommes fiers de la confiance que les professionnels placent en notre plateforme'
              }
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {stats.map((stat, index) => (
              <div key={index} className="animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
                <div className="text-4xl font-bold mb-2">{stat.number}</div>
                <div className="text-primary-100">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Team Section */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {language === 'ar' ? 'فريقنا' : 'Notre Équipe'}
            </h2>
            <p className="text-xl text-gray-600">
              {language === 'ar' 
                ? 'خبراء متخصصون في الصيدلة والتعليم يعملون من أجل نجاحك'
                : 'Des experts spécialisés en pharmacie et éducation qui travaillent pour votre succès'
              }
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
            <Heart className="h-12 w-12 text-primary-600 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {language === 'ar' ? 'بناء المستقبل معاً' : 'Construire l\'avenir ensemble'}
            </h3>
            <p className="text-gray-700 max-w-2xl mx-auto leading-relaxed">
              {language === 'ar'
                ? 'نحن فريق من المتخصصين الشغوفين بتطوير التعليم الصيدلاني. نعمل يومياً لتوفير أفضل تجربة تعليمية ممكنة لطلابنا.'
                : 'Nous sommes une équipe de professionnels passionnés par l\'amélioration de l\'éducation pharmaceutique. Nous travaillons quotidiennement pour offrir la meilleure expérience d\'apprentissage possible à nos étudiants.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
