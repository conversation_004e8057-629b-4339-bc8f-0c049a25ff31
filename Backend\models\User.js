const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  subscription_status: { type: String, enum: ['free', 'premium', 'expired'], default: 'free' },
  subscription_expires_at: { type: Date },
  created_at: { type: Date, default: Date.now }
});

module.exports = mongoose.model('User', userSchema);
