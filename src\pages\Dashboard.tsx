
import React from 'react';
import { Link } from 'react-router-dom';
import { Clock, Download, Play } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  const getSubscriptionStatus = () => {
    switch (user?.subscription_status) {
      case 'premium':
        return {
          status: 'Premium Active',
          color: 'bg-green-100 text-green-800',
          description: 'Full access to all courses'
        };
      case 'expired':
        return {
          status: 'Subscription Expired',
          color: 'bg-red-100 text-red-800',
          description: 'Renew to continue learning'
        };
      default:
        return {
          status: 'Free Plan',
          color: 'bg-gray-100 text-gray-800',
          description: 'Limited access to preview content'
        };
    }
  };

  const subscriptionInfo = getSubscriptionStatus();

  // Mock data - replace with real data from your backend
  const stats = {
    coursesCompleted: 3,
    coursesInProgress: 2,
    totalWatchTime: 45,
    certificatesEarned: 1
  };

  const recentCourses = [
    {
      id: '1',
      title: 'Clinical Pharmacology Fundamentals',
      progress: 75,
      thumbnail: 'https://images.pexels.com/photos/356040/pexels-photo-356040.jpeg',
      duration: 180
    },
    {
      id: '2',
      title: 'Pharmaceutical Calculations',
      progress: 45,
      thumbnail: 'https://images.pexels.com/photos/3279196/pexels-photo-3279196.jpeg',
      duration: 240
    },
    {
      id: '3',
      title: 'Drug Interaction Management',
      progress: 30,
      thumbnail: 'https://images.pexels.com/photos/3683074/pexels-photo-3683074.jpeg',
      duration: 150
    },
    {
      id: '4',
      title: 'Pharmaceutical Law and Ethics',
      progress: 60,
      thumbnail: 'https://images.pexels.com/photos/5668882/pexels-photo-5668882.jpeg',
      duration: 120
    }
  ];

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return hours > 0 ? `${hours}h ${remainingMinutes}m` : `${remainingMinutes}m`;
  };

  const handleDownload = (courseId: string, courseTitle: string) => {
    console.log(`Downloading course: ${courseTitle} (ID: ${courseId})`);
    // Mock download functionality
    alert(`Downloading: ${courseTitle}`);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="animate-fade-in">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.name}!
          </h1>
          <p className="text-gray-600 mt-2">
            Continue your pharmacy education journey
          </p>
        </div>

        {/* Subscription Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${subscriptionInfo.color}`}>
                {subscriptionInfo.status}
              </span>
              <p className="text-gray-600 mt-2">{subscriptionInfo.description}</p>
            </div>
            {user?.subscription_status === 'free' && (
              <Link
                to="/pricing"
                className="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
              >
                Upgrade Now
              </Link>
            )}
          </div>
        </div>

        {/* Video Library */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Your Video Library</h2>
            <Link
              to="/courses"
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              Browse All Videos
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {recentCourses.map((course) => (
              <div key={course.id} className="group cursor-pointer">
                <div className="relative overflow-hidden rounded-lg mb-3">
                  <img
                    src={course.thumbnail}
                    alt={course.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                    <Link to={`/course/${course.id}`}>
                      <Play className="h-12 w-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Link>
                  </div>
                  
                  {/* Duration overlay */}
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatDuration(course.duration)}
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-900 text-sm line-clamp-2 group-hover:text-primary-600 transition-colors">
                    {course.title}
                  </h3>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="bg-gray-200 rounded-full h-1.5">
                        <div
                          className="bg-primary-600 h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${course.progress}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">{course.progress}% complete</p>
                    </div>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownload(course.id, course.title);
                      }}
                      className="ml-3 p-2 text-gray-400 hover:text-primary-600 transition-colors"
                      title="Download video"
                    >
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
