import React from 'react';
import { Check, X, AlertCircle, Loader2 } from 'lucide-react';
import { ValidationResult, PasswordValidation } from '../../utils/validation';

interface ValidationFeedbackProps {
  validation: ValidationResult;
  isLoading?: boolean;
  className?: string;
}

export const ValidationFeedback: React.FC<ValidationFeedbackProps> = ({ 
  validation, 
  isLoading = false, 
  className = '' 
}) => {
  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 text-sm text-gray-500 ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Checking...</span>
      </div>
    );
  }

  if (!validation.message) return null;

  const getIcon = () => {
    switch (validation.type) {
      case 'success':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'error':
        return <X className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getTextColor = () => {
    switch (validation.type) {
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      case 'warning':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className={`flex items-center space-x-2 text-sm ${getTextColor()} ${className}`}>
      {getIcon()}
      <span>{validation.message}</span>
    </div>
  );
};

interface PasswordStrengthProps {
  validation: PasswordValidation;
  className?: string;
}

export const PasswordStrength: React.FC<PasswordStrengthProps> = ({ 
  validation, 
  className = '' 
}) => {
  const getStrengthColor = () => {
    switch (validation.strength) {
      case 'strong':
        return 'bg-green-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'weak':
        return 'bg-red-500';
      default:
        return 'bg-gray-300';
    }
  };

  const getStrengthWidth = () => {
    switch (validation.strength) {
      case 'strong':
        return 'w-full';
      case 'medium':
        return 'w-2/3';
      case 'weak':
        return 'w-1/3';
      default:
        return 'w-0';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Strength bar */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor()} ${getStrengthWidth()}`}
        />
      </div>
      
      {/* Strength text */}
      <div className="flex justify-between items-center">
        <span className={`text-sm font-medium ${
          validation.strength === 'strong' ? 'text-green-600' :
          validation.strength === 'medium' ? 'text-yellow-600' : 'text-red-600'
        }`}>
          {validation.strength === 'strong' ? 'Strong Password ✓' :
           validation.strength === 'medium' ? 'Medium Password' : 'Weak Password'}
        </span>
        {validation.strength === 'strong' && (
          <span className="text-xs text-green-500">All requirements met!</span>
        )}
      </div>
    </div>
  );
};

interface PasswordRequirementsProps {
  validation: PasswordValidation;
  className?: string;
}

export const PasswordRequirements: React.FC<PasswordRequirementsProps> = ({ 
  validation, 
  className = '' 
}) => {
  const requirements = [
    { key: 'startsWithUppercase', label: 'Starts with uppercase letter' },
    { key: 'hasMinLength', label: 'At least 8 characters long' },
    { key: 'hasMinLetters', label: 'Contains at least 3 letters' },
    { key: 'hasNumbers', label: 'Contains numbers' },
    { key: 'hasSpecialChar', label: 'Contains special characters (!@#$%^&*)' }
  ];

  const allRequirementsMet = requirements.every(({ key }) =>
    validation.requirements[key as keyof typeof validation.requirements]
  );

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Password Requirements:</h4>
        {allRequirementsMet && (
          <span className="text-xs text-green-600 font-medium">✓ All requirements met</span>
        )}
      </div>
      <ul className="space-y-1">
        {requirements.map(({ key, label }) => {
          const isValid = validation.requirements[key as keyof typeof validation.requirements];
          return (
            <li key={key} className="flex items-center space-x-2 text-sm">
              {isValid ? (
                <Check className="h-4 w-4 text-green-500" />
              ) : (
                <X className="h-4 w-4 text-red-500" />
              )}
              <span className={isValid ? 'text-green-600' : 'text-red-600'}>
                {label}
              </span>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

interface InputFieldProps {
  label: string;
  type: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  validation?: ValidationResult;
  isLoading?: boolean;
  placeholder?: string;
  autoComplete?: string;
  required?: boolean;
  icon?: React.ReactNode;
  showToggle?: boolean;
  onToggle?: () => void;
  className?: string;
}

export const ValidatedInput: React.FC<InputFieldProps> = ({
  label,
  type,
  name,
  value,
  onChange,
  validation,
  isLoading = false,
  placeholder,
  autoComplete,
  required = false,
  icon,
  showToggle = false,
  onToggle,
  className = ''
}) => {
  const getBorderColor = () => {
    if (!validation || !value) return 'border-gray-300 focus:border-primary-500';
    
    switch (validation.type) {
      case 'success':
        return 'border-green-500 focus:border-green-500';
      case 'error':
        return 'border-red-500 focus:border-red-500';
      case 'warning':
        return 'border-yellow-500 focus:border-yellow-500';
      default:
        return 'border-gray-300 focus:border-primary-500';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <label htmlFor={name} className="block text-sm font-medium text-gray-700">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {icon}
          </div>
        )}
        
        <input
          id={name}
          name={name}
          type={type}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          autoComplete={autoComplete}
          required={required}
          className={`block w-full ${icon ? 'pl-10' : 'pl-3'} ${showToggle ? 'pr-10' : 'pr-3'} py-3 border rounded-lg placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors ${getBorderColor()}`}
        />
        
        {showToggle && onToggle && (
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={onToggle}
          >
            {showToggle}
          </button>
        )}
        
        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
          </div>
        )}
      </div>
      
      {validation && (
        <ValidationFeedback validation={validation} isLoading={isLoading} />
      )}
    </div>
  );
};
