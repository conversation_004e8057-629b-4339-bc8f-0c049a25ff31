import React from 'react';
import { usePara<PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { Course } from '../types';
import { arabicCourses } from '../data/coursesArabic';
import VideoPlayer from '../components/video/VideoPlayer';

const CoursePreview: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { language } = useLanguage();

  const frenchCourses: Course[] = [
    {
      id: '1',
      title: 'Fondamentaux de la Pharmacologie Clinique',
      description: 'Maîtrisez les principes fondamentaux de la pharmacologie clinique incluant les mécanismes des médicaments, la pharmacocinétique et la pharmacodynamie.',
      thumbnail_url: 'https://images.pexels.com/photos/356040/pexels-photo-356040.jpeg',
      video_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      preview_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      duration: 180,
      level: 'beginner',
      category: 'Pharmacie Clinique',
      is_premium: false,
      created_at: '2024-01-15'
    },
    {
      id: '2',
      title: 'Calculs Pharmaceutiques Avancés',
      description: 'Guide complet des calculs pharmaceutiques incluant les formes galéniques, concentrations et applications cliniques.',
      thumbnail_url: 'https://images.pexels.com/photos/3279196/pexels-photo-3279196.jpeg',
      video_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      preview_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      duration: 240,
      level: 'intermediate',
      category: 'Sciences Pharmaceutiques',
      is_premium: true,
      created_at: '2024-01-10'
    },
    {
      id: '3',
      title: 'Gestion des Interactions Médicamenteuses',
      description: 'Apprenez à identifier, évaluer et gérer les interactions médicamenteuses en pratique clinique.',
      thumbnail_url: 'https://images.pexels.com/photos/3683074/pexels-photo-3683074.jpeg',
      video_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      preview_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      duration: 150,
      level: 'advanced',
      category: 'Pharmacie Clinique',
      is_premium: true,
      created_at: '2024-01-05'
    },
    {
      id: '4',
      title: 'Droit et Éthique Pharmaceutiques',
      description: 'Compréhension des responsabilités légales et éthiques dans la pratique pharmaceutique.',
      thumbnail_url: 'https://images.pexels.com/photos/5668882/pexels-photo-5668882.jpeg',
      video_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      preview_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      duration: 120,
      level: 'beginner',
      category: 'Pratique Professionnelle',
      is_premium: false,
      created_at: '2024-01-01'
    }
  ];

  const courses = language === 'ar' ? arabicCourses : frenchCourses;
  const course = courses.find(c => c.id === id);

  if (!course) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Course not found</h1>
          <Link to="/courses" className="text-primary-600 hover:text-primary-700 mt-4 inline-block">
            Back to courses
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="animate-fade-in">
        {/* Back button */}
        <Link
          to="/courses"
          className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to courses
        </Link>

        {/* Course header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
              Preview Only
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{course.title}</h1>
          <p className="text-gray-600 text-lg">{course.description}</p>
        </div>

        {/* Video player with preview overlay */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="relative">
            <VideoPlayer 
              videoUrl="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" 
              title={`${course.title} - Preview`} 
            />
            <div className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center">
              <div className="text-center text-white p-8">
                <h3 className="text-2xl font-bold mb-4">Preview Mode</h3>
                <p className="text-lg mb-6">This is a limited preview. Upgrade to premium to watch the full video.</p>
                <Link
                  to="/pricing"
                  className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 transition-colors"
                >
                  Upgrade to Premium
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoursePreview;
