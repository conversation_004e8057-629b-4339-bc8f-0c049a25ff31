# Pharmacy Auth Backend

This is a Node.js + Express REST API for authentication, designed to work with the existing React frontend. It uses MongoDB Atlas for user storage and JWT for authentication.

## Features
- User registration (signup)
- User login
- JWT authentication
- MongoDB Atlas integration

## Getting Started

1. **Install dependencies:**
   ```sh
   npm install
   ```
2. **Configure environment variables:**
   - Copy `.env.example` to `.env` and fill in the values.
3. **Start the backend server:**
   ```sh
   npm start
   ```

## API Endpoints
- `POST /api/auth/signup` — Register a new user
- `POST /api/auth/login` — Login and receive a JWT

## Running Frontend & Backend Together

You can run the backend and frontend in parallel. In the project root, add a script to launch both (see below for an example using `concurrently`).

---

**Do not change or remove any frontend code or styles.**
