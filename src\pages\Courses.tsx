import React, { useState } from 'react';
import { Search, Filter } from 'lucide-react';
import CourseCard from '../components/courses/CourseCard';
import { useLanguage } from '../contexts/LanguageContext';
import { Course } from '../types';
import { arabicCourses } from '../data/coursesArabic';

const Courses: React.FC = () => {
  const { t, language } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - replace with real API call
  const frenchCourses: Course[] = [
    {
      id: '1',
      title: 'Fondamentaux de la Pharmacologie Clinique',
      description: 'Maîtrisez les principes fondamentaux de la pharmacologie clinique incluant les mécanismes des médicaments, la pharmacocinétique et la pharmacodynamie.',
      thumbnail_url: 'https://images.pexels.com/photos/356040/pexels-photo-356040.jpeg',
      video_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      preview_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      duration: 180,
      level: 'beginner',
      category: 'Pharmacie Clinique',
      is_premium: false,
      created_at: '2024-01-15'
    },
    {
      id: '2',
      title: 'Calculs Pharmaceutiques Avancés',
      description: 'Guide complet des calculs pharmaceutiques incluant les formes galéniques, concentrations et applications cliniques.',
      thumbnail_url: 'https://images.pexels.com/photos/3279196/pexels-photo-3279196.jpeg',
      video_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      preview_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      duration: 240,
      level: 'intermediate',
      category: 'Sciences Pharmaceutiques',
      is_premium: true,
      created_at: '2024-01-10'
    },
    {
      id: '3',
      title: 'Gestion des Interactions Médicamenteuses',
      description: 'Apprenez à identifier, évaluer et gérer les interactions médicamenteuses en pratique clinique.',
      thumbnail_url: 'https://images.pexels.com/photos/3683074/pexels-photo-3683074.jpeg',
      video_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      preview_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      duration: 150,
      level: 'advanced',
      category: 'Pharmacie Clinique',
      is_premium: true,
      created_at: '2024-01-05'
    },
    {
      id: '4',
      title: 'Droit et Éthique Pharmaceutiques',
      description: 'Compréhension des responsabilités légales et éthiques dans la pratique pharmaceutique.',
      thumbnail_url: 'https://images.pexels.com/photos/5668882/pexels-photo-5668882.jpeg',
      video_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      preview_url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      duration: 120,
      level: 'beginner',
      category: 'Pratique Professionnelle',
      is_premium: false,
      created_at: '2024-01-01'
    }
  ];

  const courses = language === 'ar' ? arabicCourses : frenchCourses;

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  return (
    <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="animate-fade-in">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t('courses.title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('courses.subtitle')}
          </p>
        </div>

        {/* Search only */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex">
            <div className="flex-1">
              <div className="relative">
                <div className={`absolute inset-y-0 ${language === 'ar' ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder={t('courses.search.placeholder')}
                  className={`block w-full ${language === 'ar' ? 'pr-10 pl-3' : 'pl-10 pr-3'} py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {t('courses.showing')} {filteredCourses.length} {t('courses.of')} {courses.length} {t('nav.courses').toLowerCase()}
          </p>
        </div>

        {/* Course Grid */}
        {filteredCourses.length > 0 ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCourses.map((course, index) => (
              <div
                key={course.id}
                className="animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CourseCard course={course} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">{t('courses.noresults.title')}</h3>
            <p className="text-gray-600">
              {t('courses.noresults.desc')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Courses;
