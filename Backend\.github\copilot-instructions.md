<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This backend is a Node.js + Express REST API for authentication, designed to work with the existing React frontend. It uses MongoDB Atlas for user storage and JWT for authentication. Do not change any frontend code or styles.
