export interface User {
  id: string;
  email: string;
  name: string;
  subscription_status: 'free' | 'premium' | 'expired';
  subscription_expires_at?: string;
  created_at: string;
}

export interface Course {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string;
  video_url: string;
  preview_url?: string;
  duration: number;
  level: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  is_premium: boolean;
  created_at: string;
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateSubscription: (status: User['subscription_status']) => Promise<void>;
}