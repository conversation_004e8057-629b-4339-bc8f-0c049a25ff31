const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const auth = require('../middleware/auth');
const rateLimiter = require('../middleware/rateLimiter');

const router = express.Router();

// Validation functions
const validateEmail = (email) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

const validatePassword = (password) => {
  // Password must start with uppercase, have at least 8 chars, 3+ letters, numbers, and special chars
  const startsWithUppercase = /^[A-Z]/.test(password);
  const hasMinLength = password.length >= 8;
  const hasLetters = /[a-zA-Z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
  const hasMinLetters = (password.match(/[a-zA-Z]/g) || []).length >= 3;

  return {
    isValid: startsWithUppercase && hasMinLength && hasLetters && hasNumbers && hasSpecialChar && hasMinLetters,
    requirements: {
      startsWithUppercase,
      hasMinLength,
      hasLetters,
      hasNumbers,
      hasSpecialChar,
      hasMinLetters
    }
  };
};

const validateName = (name) => {
  return name && name.trim().length >= 2 && /^[a-zA-Z\s]+$/.test(name);
};

// Signup with rate limiting
router.post('/signup', rateLimiter(3, 10 * 60 * 1000), async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Validate required fields
    if (!name || !email || !password) {
      return res.status(400).json({ message: 'All fields are required.' });
    }

    // Validate name
    if (!validateName(name)) {
      return res.status(400).json({ message: 'Name must be at least 2 characters and contain only letters and spaces.' });
    }

    // Validate email format
    if (!validateEmail(email)) {
      return res.status(400).json({ message: 'Please enter a valid email address.' });
    }

    // Validate password
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      const missing = [];
      if (!passwordValidation.requirements.startsWithUppercase) missing.push('start with uppercase letter');
      if (!passwordValidation.requirements.hasMinLength) missing.push('be at least 8 characters');
      if (!passwordValidation.requirements.hasMinLetters) missing.push('contain at least 3 letters');
      if (!passwordValidation.requirements.hasNumbers) missing.push('contain numbers');
      if (!passwordValidation.requirements.hasSpecialChar) missing.push('contain special characters');

      return res.status(400).json({
        message: `Password must ${missing.join(', ')}.`,
        passwordRequirements: passwordValidation.requirements
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(409).json({ message: 'An account with this email already exists.' });
    }

    // Hash password and create user
    const hashedPassword = await bcrypt.hash(password, 12);
    const user = new User({
      name: name.trim(),
      email: email.toLowerCase(),
      password: hashedPassword
    });

    await user.save();

    // Generate JWT token
    const token = jwt.sign({ id: user._id, email: user.email }, process.env.JWT_SECRET, { expiresIn: '7d' });

    res.status(201).json({
      token,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        subscription_status: user.subscription_status,
        subscription_expires_at: user.subscription_expires_at,
        created_at: user.created_at
      }
    });
  } catch (err) {
    console.error('Signup error:', err);
    res.status(500).json({ message: 'Server error. Please try again later.' });
  }
});

// Login with rate limiting
router.post('/login', rateLimiter(5, 15 * 60 * 1000), async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({ message: 'Email and password are required.' });
    }

    // Validate email format
    if (!validateEmail(email)) {
      return res.status(400).json({ message: 'Please enter a valid email address.' });
    }

    // Find user by email (case insensitive)
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(401).json({ message: 'Invalid email or password.' });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ message: 'Invalid email or password.' });
    }

    // Generate JWT token
    const token = jwt.sign({ id: user._id, email: user.email }, process.env.JWT_SECRET, { expiresIn: '7d' });

    res.json({
      token,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        subscription_status: user.subscription_status,
        subscription_expires_at: user.subscription_expires_at,
        created_at: user.created_at
      }
    });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({ message: 'Server error. Please try again later.' });
  }
});

// Get user profile
router.get('/profile', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    res.json({ user: { id: user._id, email: user.email, name: user.name, subscription_status: user.subscription_status, subscription_expires_at: user.subscription_expires_at, created_at: user.created_at } });
  } catch (err) {
    console.error('Profile fetch error:', err);
    res.status(500).json({ message: 'Server error.' });
  }
});

// Update subscription
router.put('/subscription', auth, async (req, res) => {
  try {
    const { subscription_status, subscription_expires_at } = req.body;

    const user = await User.findByIdAndUpdate(
      req.user.id,
      { subscription_status, subscription_expires_at },
      { new: true }
    ).select('-password');

    res.json({ user: { id: user._id, email: user.email, name: user.name, subscription_status: user.subscription_status, subscription_expires_at: user.subscription_expires_at, created_at: user.created_at } });
  } catch (err) {
    console.error('Subscription update error:', err);
    res.status(500).json({ message: 'Server error.' });
  }
});

module.exports = router;
